<?php
// This file is part of Moodle - http://moodle.org/
//
// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Thank you page for stripepaymentpro plugin - handles both Checkout Sessions and PaymentIntents
 *
 * @package    enrol_stripepaymentpro
 * <AUTHOR> <<EMAIL>>
 * @copyright  2023 DualCube Team(https://dualcube.com)
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

require_once('../../config.php');
require_once($CFG->dirroot . '/enrol/stripepaymentpro/lib.php');

global $DB, $USER, $CFG;

// Get parameters
$session_id = optional_param('session_id', '', PARAM_RAW);
$payment_intent_id = optional_param('payment_intent', '', PARAM_RAW);

// Require login
require_login();

// Set up Stripe
$secretkey = get_config('enrol_stripepayment', 'secretkey');
if (empty($secretkey)) {
    print_error('Stripe configuration incomplete');
}

\Stripe\Stripe::setApiKey($secretkey);

$PAGE->set_context(context_system::instance());
$PAGE->set_url('/enrol/stripepaymentpro/thankyou.php');
$PAGE->set_title(get_string('payment_successful', 'enrol_stripepaymentpro'));
$PAGE->set_heading(get_string('payment_successful', 'enrol_stripepaymentpro'));

echo $OUTPUT->header();

try {
    if (!empty($session_id)) {
        // Handle Checkout Session (existing logic)
        $session = \Stripe\Checkout\Session::retrieve($session_id);
        
        if ($session->payment_status === 'paid') {
            // Get metadata
            $userid = $session->metadata->userid ?? $USER->id;
            $instanceid = $session->metadata->instanceid ?? 0;
            $couponid = $session->metadata->couponid ?? '';
            
            // Process enrollment
            if ($instanceid) {
                $instance = $DB->get_record('enrol', ['id' => $instanceid], '*', MUST_EXIST);
                $plugin = enrol_get_plugin('stripepaymentpro');
                
                // Enroll user
                $plugin->enrol_user($instance, $userid, $instance->roleid, time(), 0);
                
                // Store payment record
                $payment_record = new stdClass();
                $payment_record->userid = $userid;
                $payment_record->instanceid = $instanceid;
                $payment_record->payment_status = 'completed';
                $payment_record->txn_id = $session->payment_intent;
                $payment_record->timeupdated = time();
                $DB->insert_record('enrol_stripepaymentpro', $payment_record);
                
                echo $OUTPUT->notification(get_string('payment_successful_message', 'enrol_stripepaymentpro'), 'success');
                echo html_writer::link(new moodle_url('/course/view.php', ['id' => $instance->courseid]), 
                    get_string('continue_to_course', 'enrol_stripepaymentpro'), ['class' => 'btn btn-primary']);
            }
        } else {
            echo $OUTPUT->notification(get_string('payment_failed', 'enrol_stripepaymentpro'), 'error');
        }
        
    } elseif (!empty($payment_intent_id)) {
        // Handle PaymentIntent (Elements mode)
        $payment_intent = \Stripe\PaymentIntent::retrieve($payment_intent_id);
        
        if ($payment_intent->status === 'succeeded') {
            // Get metadata
            $userid = $payment_intent->metadata->userid ?? $USER->id;
            $instanceid = $payment_intent->metadata->instanceid ?? 0;
            $couponid = $payment_intent->metadata->couponid ?? '';
            
            // Process enrollment
            if ($instanceid) {
                $instance = $DB->get_record('enrol', ['id' => $instanceid], '*', MUST_EXIST);
                $plugin = enrol_get_plugin('stripepaymentpro');
                
                // Enroll user
                $plugin->enrol_user($instance, $userid, $instance->roleid, time(), 0);
                
                // Store payment record
                $payment_record = new stdClass();
                $payment_record->userid = $userid;
                $payment_record->instanceid = $instanceid;
                $payment_record->payment_status = 'completed';
                $payment_record->txn_id = $payment_intent_id;
                $payment_record->timeupdated = time();
                $DB->insert_record('enrol_stripepaymentpro', $payment_record);
                
                echo $OUTPUT->notification(get_string('payment_successful_message', 'enrol_stripepaymentpro'), 'success');
                echo html_writer::link(new moodle_url('/course/view.php', ['id' => $instance->courseid]), 
                    get_string('continue_to_course', 'enrol_stripepaymentpro'), ['class' => 'btn btn-primary']);
            }
        } else {
            echo $OUTPUT->notification(get_string('payment_failed', 'enrol_stripepaymentpro'), 'error');
        }
        
    } else {
        echo $OUTPUT->notification(get_string('invalid_payment_session', 'enrol_stripepaymentpro'), 'error');
    }
    
} catch (Exception $e) {
    echo $OUTPUT->notification(get_string('payment_error', 'enrol_stripepaymentpro') . ': ' . $e->getMessage(), 'error');
}

echo $OUTPUT->footer();
